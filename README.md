# Satellite Tracker Application

A cross-platform satellite tracking application that automatically adapts to different environments.

## Features

- **Cross-Platform**: Works on both Windows and Linux/Debian without platform-specific dependencies
- **Environment Detection**: Automatically runs in development mode on Windows and production mode on Debian
- **No External Dependencies**: No longer requires PM2, PostgreSQL, or Nginx
- **In-Memory Storage**: Uses efficient in-memory storage for satellite data and user management
- **Real-time Updates**: WebSocket support for live satellite tracking

## Quick Start

### Windows
```cmd
# Double-click start.bat or run in command prompt:
start.bat
```

### Linux/Debian
```bash
# Make script executable and run:
chmod +x start.sh
./start.sh
```

### Manual Start
```bash
# Install dependencies
npm install

# Build the application
npm run build

# Start in development mode
npm run start:dev

# Start in production mode
npm run start:prod
```

## Environment Configuration

The application automatically detects the platform and configures itself:

- **Windows**: Runs in development mode by default
- **Linux/Debian**: Runs in production mode by default

You can override this by setting the `NODE_ENV` environment variable:

```bash
# Force production mode
export NODE_ENV=production

# Force development mode
export NODE_ENV=development
```

## Port Configuration

- **Application**: Port 5000 (serves both API and client)
- **Development Server**: Port 5173 (Vite dev server, development only)
- **TCP Satellite Data**: Port 8090

## User Management

Users are managed via CSV file (`users.csv`). The format is:
```csv
username,password,imeis,role
admin,password123,"IMEI1,IMEI2",admin
user1,userpass,"IMEI3",user
```

## API Endpoints

- `POST /api/auth/login` - User authentication
- `GET /api/messages` - Get satellite messages
- `GET /api/trips` - Get trip data
- `WebSocket /ws` - Real-time updates

## Development

```bash
# Start development server with hot reload
npm run dev

# Type checking
npm run check

# Build for production
npm run build
```

## Troubleshooting

### Port Already in Use
If port 5000 is already in use, you can change it by setting the PORT environment variable:
```bash
export PORT=3000
npm run start:prod
```

### Build Issues
Make sure you have Node.js 18+ installed:
```bash
node --version
npm --version
```

### WebSocket Connection Issues
Ensure your firewall allows connections on port 5000 and 8090.

## Architecture

- **Frontend**: React with Vite
- **Backend**: Express.js with TypeScript
- **Storage**: In-memory storage (no database required)
- **Real-time**: WebSocket for live updates
- **Authentication**: Session-based with CSV user storage
