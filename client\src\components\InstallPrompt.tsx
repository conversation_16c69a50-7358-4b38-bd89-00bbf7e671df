import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { X, Download } from "lucide-react";

export default function InstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);

  useEffect(() => {
    // Controlla se l'app è già installata (modalità standalone)
    const checkIfStandalone = () => {
      return window.matchMedia('(display-mode: standalone)').matches || 
             (window.navigator as any).standalone === true;
    };
    
    setIsStandalone(checkIfStandalone());
    
    // Se l'app è già installata, non mostrare il prompt
    if (checkIfStandalone()) {
      return;
    }

    // Controlla se l'utente ha già chiuso il prompt in questa sessione di login
    const hasClosedInSession = sessionStorage.getItem('install-prompt-closed-session');
    const hasClosedPermanently = localStorage.getItem('install-prompt-closed-permanent');
    
    if (hasClosedPermanently) {
      return;
    }

    const handler = (e: any) => {
      e.preventDefault();
      setDeferredPrompt(e);
      
      // Mostra il prompt solo se non è stato chiuso in questa sessione
      if (!hasClosedInSession) {
        // Piccolo delay per evitare che il prompt appaia troppo presto
        setTimeout(() => setShowPrompt(true), 1500);
      }
    };

    window.addEventListener("beforeinstallprompt", handler);

    // Controllo periodico per vedere se l'app diventa installabile
    const checkInstallability = () => {
      if (!deferredPrompt && !hasClosedInSession) {
        // Simula il prompt se i criteri PWA sono soddisfatti ma l'evento non è scatenato
        const isHTTPS = window.location.protocol === 'https:' || window.location.hostname === 'localhost';
        const hasManifest = document.querySelector('link[rel="manifest"]');
        const hasServiceWorker = 'serviceWorker' in navigator;
        
        if (isHTTPS && hasManifest && hasServiceWorker) {
          setTimeout(() => {
            if (!showPrompt && !isStandalone) {
              setShowPrompt(true);
            }
          }, 3000);
        }
      }
    };

    const installCheckTimer = setTimeout(checkInstallability, 2000);

    return () => {
      window.removeEventListener("beforeinstallprompt", handler);
      clearTimeout(installCheckTimer);
    };
  }, [deferredPrompt, showPrompt, isStandalone]);

  const handleInstallClick = async () => {
    if (deferredPrompt) {
      try {
        deferredPrompt.prompt();
        const { outcome } = await deferredPrompt.userChoice;
        
        if (outcome === "accepted") {
          setShowPrompt(false);
          localStorage.setItem('install-prompt-closed-permanent', 'true');
        } else if (outcome === "dismissed") {
          setShowPrompt(false);
          sessionStorage.setItem('install-prompt-closed-session', 'true');
        }
      } catch (error) {
        console.log('Errore durante l\'installazione:', error);
        setShowPrompt(false);
        sessionStorage.setItem('install-prompt-closed-session', 'true');
      }
    } else {
      // Fallback: informa l'utente su come installare manualmente
      setShowPrompt(false);
      sessionStorage.setItem('install-prompt-closed-session', 'true');
    }
  };

  const handleCloseClick = () => {
    setShowPrompt(false);
    sessionStorage.setItem('install-prompt-closed-session', 'true');
  };

  const handleNeverShowClick = () => {
    setShowPrompt(false);
    localStorage.setItem('install-prompt-closed-permanent', 'true');
  };

  // Non mostrare se l'app è già installata
  if (!showPrompt || isStandalone) return null;

  return (
    <div className="fixed bottom-4 left-1/2 -translate-x-1/2 bg-white shadow-xl rounded-lg p-4 max-w-sm w-full mx-4 z-50 border border-gray-200">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-1">
          <Download className="h-5 w-5 text-green-600" />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900">
            Installa l'app
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Aggiungi ECOTrac alla schermata home per un accesso rapido
          </p>
        </div>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleCloseClick} 
          className="p-1 flex-shrink-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      <div className="flex space-x-2 mt-3">
        <Button 
          onClick={handleInstallClick} 
          size="sm" 
          className="flex-1 text-xs"
        >
          Installa
        </Button>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleNeverShowClick}
          className="text-xs"
        >
          Non mostrare più
        </Button>
      </div>
    </div>
  );
} 