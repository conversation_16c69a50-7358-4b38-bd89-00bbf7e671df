import { useEffect, useRef, useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>hai<PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { SatelliteMessage, Trip } from "@shared/schema";
import { getTripColor } from "@/lib/utils";

// Leaflet imports
import L from "leaflet";
import "leaflet/dist/leaflet.css";

// Fix for default markers in Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
});

interface MapSectionProps {
  messages: SatelliteMessage[];
  trips: Trip[];
  selectedPosition: { lat: number; lng: number } | null;
  selectedMessageId: number | null;
  onMarkerClick: (messageId: number, lat: number, lng: number) => void;
  isLoading: boolean;
}

export default function MapSection({
  messages,
  trips,
  selectedPosition,
  selectedMessageId,
  onMarkerClick,
  isLoading,
}: MapSectionProps) {
  const mapRef = useRef<L.Map | null>(null);
  const markersRef = useRef<Map<number, L.CircleMarker | L.Marker>>(new Map());
  const polylinesRef = useRef<Map<string, L.Polyline>>(new Map());
  const [hasCentered, setHasCentered] = useState(false);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) {
      const map = L.map('satellite-map').setView([45.4642, 9.1900], 13);
      
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        // attribution: '© OpenStreetMap contributors' // Rimosso per eliminare il logo Leaflet
      }).addTo(map);
      
      mapRef.current = map;
    }

    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
    };
  }, []);

  // Update markers and polylines when messages change
  useEffect(() => {
    if (!mapRef.current || !messages.length) return;

    const map = mapRef.current;
    
    // Clear existing markers and polylines
    markersRef.current.forEach(marker => map.removeLayer(marker));
    markersRef.current.clear();
    
    polylinesRef.current.forEach(polyline => map.removeLayer(polyline));
    polylinesRef.current.clear();

    // Group messages by tripId
    const messagesByTrip = new Map<string, SatelliteMessage[]>();
    
    messages.forEach(message => {
      if (message.tripId) {
        if (!messagesByTrip.has(message.tripId)) {
          messagesByTrip.set(message.tripId, []);
        }
        messagesByTrip.get(message.tripId)!.push(message);
      }
    });

    // Create polylines for each trip
    messagesByTrip.forEach((tripMessages, tripId) => {
      if (tripMessages.length > 1) {
        // Sort messages by timestamp for proper polyline
        const sortedMessages = [...tripMessages].sort((a, b) => 
          new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime()
        );

        const coordinates: [number, number][] = sortedMessages.map(msg => [msg.latitude, msg.longitude]);
        const tripColor = getTripColor(tripId);
        
        const polyline = L.polyline(coordinates, {
          color: tripColor,
          weight: 4,
          opacity: 0.7
        }).addTo(map);

        polylinesRef.current.set(tripId, polyline);
      }
    });

    // Add markers for each position
    messages.forEach((message) => {
      const color = getStatusColor(message.status);
      const isStartOrEnd = message.status === 'Start' || message.status === 'End';
      
      let marker;
      
      if (isStartOrEnd) {
        // Use custom icon for Start/End markers
        const iconHtml = message.status === 'Start' ? '▶️' : '⏹️';
        const iconColor = message.status === 'Start' ? '#4CAF50' : '#F44336';
        
        const customIcon = L.divIcon({
          html: `<div style="
            background-color: ${iconColor};
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          ">${iconHtml}</div>`,
          className: 'custom-marker',
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        });
        
        marker = L.marker([message.latitude, message.longitude], {
          icon: customIcon
        }).addTo(map);
      } else {
        // Use circle marker for other statuses
        let markerOptions;
        if (selectedMessageId === message.id) {
          // Marker selezionato: colore brillante e bordo spesso
          markerOptions = {
            color: '#FFD600', // Giallo brillante
            fillColor: '#FFFF00', // Giallo pieno
            fillOpacity: 1,
            radius: 10,
            weight: 5
          };
        } else {
          markerOptions = {
            color: color,
            fillColor: color,
            fillOpacity: 0.8,
            radius: 6,
            weight: 2
          };
        }
        marker = L.circleMarker([message.latitude, message.longitude], markerOptions).addTo(map);
      }

      const timestamp = new Date(message.satelliteTimestamp).toLocaleString('it-IT', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
      
      const statusText = getStatusText(message.status);
      
      marker.bindPopup(`
        <div class="p-2">
          <div class="font-medium">${timestamp}</div>
          <div class="text-sm text-gray-600">${message.latitude.toFixed(6)}, ${message.longitude.toFixed(6)}</div>
          <div class="text-sm">Velocità: ${message.speed} km/h</div>
          <div class="text-sm">Direzione: ${message.direction}°</div>
          <div class="text-sm">Batteria: ${message.batteryPercentage}%</div>
          <div class="text-sm">Stato: ${statusText}</div>
        </div>
      `);

      marker.on('click', () => {
        onMarkerClick(message.id, message.latitude, message.longitude);
      });

      markersRef.current.set(message.id, marker);
    });

    // Fit map to show all markers SOLO al primo caricamento
    if (!hasCentered && messages.length > 0) {
      const group = new L.FeatureGroup(Array.from(markersRef.current.values()));
      map.fitBounds(group.getBounds().pad(0.1));
      setHasCentered(true);
    }
  }, [messages, selectedMessageId, onMarkerClick, hasCentered]);

  // Quando cambia IMEI o viaggio, resetta la centratura
  useEffect(() => {
    setHasCentered(false);
  }, [messages.length]);

  // Center map on selected position
  useEffect(() => {
    if (selectedPosition && mapRef.current) {
      mapRef.current.setView([selectedPosition.lat, selectedPosition.lng], mapRef.current.getZoom());
    }
  }, [selectedPosition]);

  // Handle map resize when container height changes
  useEffect(() => {
    const handleResize = () => {
      if (mapRef.current) {
        mapRef.current.invalidateSize();
      }
    };

    // Use ResizeObserver to detect container size changes
    const mapContainer = document.getElementById('satellite-map');
    if (mapContainer) {
      const resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(mapContainer);
      
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, []);

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Fixed':
        return '#388E3C';
      case 'No Fix':
        return '#D32F2F';
      case 'Start':
        return '#4CAF50'; // Verde per Inizio
      case 'End':
        return '#F44336'; // Rosso per Fine
      default:
        return '#757575';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'Fixed':
        return 'GPS fixed';
      case 'No Fix':
        return 'GPS not fixed';
      case 'Start':
        return 'Start';
      case 'End':
        return 'Fine';
      default:
        return status;
    }
  };

  const handleZoomIn = () => {
    if (mapRef.current) {
      mapRef.current.zoomIn();
    }
  };

  const handleZoomOut = () => {
    if (mapRef.current) {
      mapRef.current.zoomOut();
    }
  };

  const handleCenter = () => {
    if (mapRef.current && messages.length > 0) {
      const group = new L.FeatureGroup(Array.from(markersRef.current.values()));
      mapRef.current.fitBounds(group.getBounds().pad(0.1));
    }
  };

  return (
    <div className="h-full relative bg-white border-b border-gray-200 overflow-hidden">
      <div id="satellite-map" className="h-full w-full" />
      
      {/* Map controls overlay */}
      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3 space-y-2">
        <Button
          size="sm"
          variant="ghost"
          onClick={handleZoomIn}
          className="w-8 h-8 p-0 bg-gray-100 hover:bg-gray-200"
        >
          <Plus className="h-4 w-4 text-gray-600" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={handleZoomOut}
          className="w-8 h-8 p-0 bg-gray-100 hover:bg-gray-200"
        >
          <Minus className="h-4 w-4 text-gray-600" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={handleCenter}
          className="w-8 h-8 p-0 bg-gray-100 hover:bg-gray-200"
        >
          <Crosshair className="h-4 w-4 text-gray-600" />
        </Button>
      </div>

      {/* Status indicators */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3">
        <div className="text-xs text-gray-600 mb-2">Legenda Stati:</div>
        <div className="space-y-1 text-xs">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-600 rounded-full mr-2"></div>
            <span>Fisso</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-600 rounded-full mr-2"></div>
            <span>Nessun Segnale</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span>Inizio</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
            <span>Fine</span>
          </div>
        </div>
      </div>

      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="text-lg text-gray-600">Caricamento dati mappa...</div>
        </div>
      )}
    </div>
  );
}
