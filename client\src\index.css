@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(120, 25%, 98%);
  --foreground: hsl(120, 15%, 12%);
  --muted: hsl(120, 20%, 95%);
  --muted-foreground: hsl(120, 10%, 45%);
  --popover: hsl(120, 25%, 98%);
  --popover-foreground: hsl(120, 15%, 12%);
  --card: hsl(120, 25%, 98%);
  --card-foreground: hsl(120, 15%, 12%);
  --border: hsl(120, 20%, 90%);
  --input: hsl(120, 20%, 90%);
  --primary: hsl(120, 40%, 35%);
  --primary-foreground: hsl(120, 25%, 98%);
  --secondary: hsl(120, 20%, 95%);
  --secondary-foreground: hsl(120, 15%, 12%);
  --accent: hsl(120, 25%, 92%);
  --accent-foreground: hsl(120, 15%, 12%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(120, 25%, 98%);
  --ring: hsl(120, 40%, 35%);
  --radius: 0.75rem;
  --success: hsl(120, 40%, 35%);
  --warning: hsl(35, 91%, 48%);
  --danger: hsl(0, 72%, 51%);
}

.dark {
  --background: hsl(120, 10%, 8%);
  --foreground: hsl(120, 20%, 95%);
  --muted: hsl(120, 8%, 15%);
  --muted-foreground: hsl(120, 15%, 65%);
  --popover: hsl(120, 10%, 8%);
  --popover-foreground: hsl(120, 20%, 95%);
  --card: hsl(120, 10%, 8%);
  --card-foreground: hsl(120, 20%, 95%);
  --border: hsl(120, 8%, 15%);
  --input: hsl(120, 8%, 15%);
  --primary: hsl(120, 40%, 35%);
  --primary-foreground: hsl(120, 20%, 95%);
  --secondary: hsl(120, 8%, 15%);
  --secondary-foreground: hsl(120, 20%, 95%);
  --accent: hsl(120, 12%, 12%);
  --accent-foreground: hsl(120, 20%, 95%);
  --destructive: hsl(0, 62%, 30%);
  --destructive-foreground: hsl(120, 20%, 95%);
  --ring: hsl(120, 40%, 35%);
  --radius: 0.75rem;
  --success: hsl(120, 40%, 35%);
  --warning: hsl(35, 91%, 48%);
  --danger: hsl(0, 72%, 51%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-br from-green-100 to-green-200 text-foreground;
    font-family: 'Inter', 'Roboto', sans-serif;
    background-attachment: fixed;
  }
}

/* Leaflet map styles */
.leaflet-container {
  font-family: 'Inter', 'Roboto', sans-serif;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Custom scrollbar for table */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(120, 20%, 95%);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(120, 40%, 35%);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(120, 40%, 30%);
}

/* Ensure select dropdown appears above map */
[data-radix-select-content] {
  z-index: 9999 !important;
  position: fixed !important;
}

.leaflet-container {
  z-index: 1 !important;
}

/* Hide Leaflet attribution/logo */
.leaflet-control-attribution {
  display: none !important;
}

.leaflet-control-attribution.leaflet-control {
  display: none !important;
}

/* Resizable handle styles */
.resizable-handle {
  position: relative;
  z-index: 10;
  background: linear-gradient(to top, hsl(120, 40%, 35%), hsl(120, 40%, 40%)) !important;
}

.resizable-handle:hover {
  background: linear-gradient(to top, hsl(120, 40%, 30%), hsl(120, 40%, 35%)) !important;
}

.resizable-handle:active {
  background: linear-gradient(to top, hsl(120, 40%, 25%), hsl(120, 40%, 30%)) !important;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Custom button styles */
.btn-green {
  background: linear-gradient(135deg, hsl(120, 40%, 35%) 0%, hsl(120, 40%, 40%) 100%);
  color: white;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(34, 197, 94, 0.2);
}

.btn-green:hover {
  background: linear-gradient(135deg, hsl(120, 40%, 30%) 0%, hsl(120, 40%, 35%) 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(34, 197, 94, 0.3);
}

.btn-green:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px -1px rgba(34, 197, 94, 0.2);
}

/* Custom input styles */
.input-green {
  border-color: hsl(120, 20%, 90%);
  transition: all 0.2s ease;
}

.input-green:focus {
  border-color: hsl(120, 40%, 35%);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Custom select styles */
.select-green {
  border-color: hsl(120, 20%, 90%);
  transition: all 0.2s ease;
}

.select-green:focus {
  border-color: hsl(120, 40%, 35%);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Focus styles */
.focus-green:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
  border-color: hsl(120, 40%, 35%);
}

/* Table row hover effects */
.table-row-hover {
  transition: all 0.2s ease;
}

.table-row-hover:hover {
  background-color: hsl(120, 25%, 95%) !important;
  transform: scale(1.001);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px -10px rgba(0, 0, 0, 0.1);
}
