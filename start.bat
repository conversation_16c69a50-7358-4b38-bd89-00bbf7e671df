@echo off
echo Starting Satellite Tracker Application...
echo ========================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Check if build exists
if not exist "dist" (
    echo Building application...
    npm run build
    if %errorlevel% neq 0 (
        echo ERROR: Build failed
        pause
        exit /b 1
    )
)

REM Detect environment and start accordingly
if "%NODE_ENV%"=="production" (
    echo Starting in PRODUCTION mode...
    npm run start:prod
) else (
    echo Starting in DEVELOPMENT mode...
    echo You can also run: npm run dev
    npm run start:dev
)

pause
