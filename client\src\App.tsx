import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import TrackingPage from "@/pages/tracking";
import LoginPage from "@/pages/login";
import AdminPage from "@/pages/admin";
import { useAuth } from "@/hooks/use-auth";

function Router() {
  const { user, isLoading, login, logout } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg text-gray-600">Caricamento...</div>
      </div>
    );
  }

  return (
    <Switch>
      <Route path="/admin">
        {user && user.role === "admin" ? (
          <AdminPage />
        ) : (
          <LoginPage onLogin={login} />
        )}
      </Route>
      <Route path="/login">
        {user ? <TrackingPage user={user} onLogout={logout} /> : <LoginPage onLogin={login} />}
      </Route>
      <Route path="/">
        {user ? <TrackingPage user={user} onLogout={logout} /> : <LoginPage onLogin={login} />}
      </Route>
    </Switch>
  );
}

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Router />
        <Toaster />
      </TooltipProvider>
    </QueryClientProvider>
  );
}
