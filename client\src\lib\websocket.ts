import type { SatelliteMessage, WebSocketMessage } from "@shared/schema";
import { toast } from "@/hooks/use-toast";

export class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private messageHandlers: ((message: WebSocketMessage) => void)[] = [];
  private isConnecting = false;

  connect(): void {
    if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      // Costruzione URL WebSocket: usa localhost:5000 in sviluppo
      let wsUrl;
      if (process.env.NODE_ENV === "development") {
        wsUrl = "ws://localhost:5000/ws";
      } else {
        const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
        wsUrl = `${protocol}//${window.location.host}/ws`;
      }
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.isConnecting = false;
        
        // Clear any pending reconnection
        if (this.reconnectTimeout) {
          clearTimeout(this.reconnectTimeout);
          this.reconnectTimeout = null;
        }
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.messageHandlers.forEach(handler => handler(message));
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.isConnecting = false;
        this.ws = null;
        
        // Attempt to reconnect after 3 seconds
        this.reconnectTimeout = setTimeout(() => {
          this.connect();
        }, 3000);
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.isConnecting = false;
      };
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      this.isConnecting = false;
    }
  }

  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  onMessage(handler: (message: WebSocketMessage) => void): () => void {
    this.messageHandlers.push(handler);
    
    // Return unsubscribe function
    return () => {
      const index = this.messageHandlers.indexOf(handler);
      if (index > -1) {
        this.messageHandlers.splice(index, 1);
      }
    };
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

export const websocketManager = new WebSocketManager();

// WebSocketMessage is already defined in shared/schema.ts

// Funzione per gestire le notifiche in-app
export const handleInAppNotification = (notificationData: any) => {
  // Mostra toast in-app
  toast({
    title: notificationData.title,
    description: notificationData.body,
  });

  // Verifica se le notifiche sono supportate e autorizzate
  if ('Notification' in window && Notification.permission === 'granted') {
    // Mostra sempre la notifica nativa del browser
    new Notification(notificationData.title, {
      body: notificationData.body,
      icon: '/assets/logo-ecotrac.png',
      tag: 'ecotrac-notification',
      requireInteraction: false, // La notifica si chiude automaticamente
    });
  }
  
  // Puoi aggiungere qui altre logiche per notifiche in-app
  console.log('In-app notification:', notificationData);
};
