import { useState, useEffect } from 'react';
import { AUTH_CONFIG } from '@/config/auth';

export interface User {
  id: number;
  username: string;
  imeis: string[];
  role: string;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Controlla se la sessione è valida
  const isSessionValid = (loginTime: string): boolean => {
    const loginTimestamp = parseInt(loginTime);
    const currentTime = Date.now();
    return currentTime - loginTimestamp <= AUTH_CONFIG.SESSION_TIMEOUT;
  };

  // Carica l'utente dal localStorage
  const loadUser = (): User | null => {
    const savedUser = localStorage.getItem(AUTH_CONFIG.STORAGE_KEYS.USER);
    const loginTime = localStorage.getItem(AUTH_CONFIG.STORAGE_KEYS.LOGIN_TIME);
    
    if (!savedUser || !loginTime) {
      return null;
    }

    try {
      const userData = JSON.parse(savedUser);
      
      // Controlla se la sessione è scaduta
      if (!isSessionValid(loginTime)) {
        console.log('Sessione scaduta, logout automatico');
        logout();
        return null;
      }
      
      return userData;
    } catch (error) {
      console.error('Error parsing saved user:', error);
      logout();
      return null;
    }
  };

  // Login dell'utente
  const login = (userData: User) => {
    setUser(userData);
    localStorage.setItem(AUTH_CONFIG.STORAGE_KEYS.USER, JSON.stringify(userData));
    localStorage.setItem(AUTH_CONFIG.STORAGE_KEYS.LOGIN_TIME, Date.now().toString());
  };

  // Logout dell'utente
  const logout = () => {
    setUser(null);
    localStorage.removeItem(AUTH_CONFIG.STORAGE_KEYS.USER);
    localStorage.removeItem(AUTH_CONFIG.STORAGE_KEYS.LOGIN_TIME);
  };

  // Controlla periodicamente se la sessione è scaduta
  useEffect(() => {
    const checkSession = () => {
      const loginTime = localStorage.getItem(AUTH_CONFIG.STORAGE_KEYS.LOGIN_TIME);
      if (loginTime && !isSessionValid(loginTime)) {
        console.log('Sessione scaduta durante l\'uso, logout automatico');
        logout();
      }
    };

    // Controlla ogni minuto
    const interval = setInterval(checkSession, AUTH_CONFIG.SESSION_CHECK_INTERVAL);
    
    return () => clearInterval(interval);
  }, []);

  // Gestione chiusura browser/tab
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Rimuovi i dati di sessione quando si chiude il browser
      localStorage.removeItem(AUTH_CONFIG.STORAGE_KEYS.USER);
      localStorage.removeItem(AUTH_CONFIG.STORAGE_KEYS.LOGIN_TIME);
    };

    const handlePageHide = () => {
      // Fallback per browser che non supportano beforeunload
      localStorage.removeItem(AUTH_CONFIG.STORAGE_KEYS.USER);
      localStorage.removeItem(AUTH_CONFIG.STORAGE_KEYS.LOGIN_TIME);
    };

    // Aggiungi event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('pagehide', handlePageHide);

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('pagehide', handlePageHide);
    };
  }, []);

  // Inizializzazione
  useEffect(() => {
    const savedUser = loadUser();
    setUser(savedUser);
    setIsLoading(false);
  }, []);

  return {
    user,
    isLoading,
    login,
    logout,
    isAuthenticated: !!user,
  };
} 