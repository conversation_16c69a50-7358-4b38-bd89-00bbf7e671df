import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Array di colori per i viaggi
export const TRIP_COLORS = [
  '#FF1744', // Rosso brillante
  '#F50057', // Rosa acceso
  '#D500F9', // Viola acceso
  '#651FFF', // Blu/viola acceso
  '#2979FF', // Blu brillante
  '#00B8D4', // Ciano acceso
  '#1DE9B6', // Verde acqua brillante
  '#00E676', // Verde brillante
  '#76FF03', // Verde lime
  '#FFEA00', // Giallo brillante
  '#FFC400', // Giallo/arancio
  '#FF9100', // Arancione acceso
  '#FF3D00', // Arancione/rosso acceso
  '#AEEA00', // Verde lime chiaro
  '#00BFAE', // Turchese acceso
  '#FF80AB', // Rosa chiaro acceso
];

// Funzione per generare un colore basato sull'ID del viaggio
export const getTripColor = (tripId: string): string => {
  // Usa l'hash dell'ID per selezionare un colore consistente
  let hash = 0;
  for (let i = 0; i < tripId.length; i++) {
    const char = tripId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  const index = Math.abs(hash) % TRIP_COLORS.length;
  return TRIP_COLORS[index];
};

// Utility per scaricare un file dal browser
export function downloadFile(filename: string, content: string, mimeType = 'application/octet-stream') {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  setTimeout(() => {
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, 0);
}

// Esporta un array di SatelliteMessage in formato KML
export function exportMessagesToKml(messages: Array<{
  id: number;
  imei: string;
  satelliteTimestamp: string;
  latitude: number;
  longitude: number;
  speed: number;
  direction: number;
  batteryPercentage: number;
  status: string;
  tripId?: string;
}>): string {
  const kmlHeader = `<?xml version="1.0" encoding="UTF-8"?>
      <kml xmlns="http://www.opengis.net/kml/2.2">
        <Document>
          <name>Satellite Messages</name>
          `;
  const kmlFooter = `
        </Document>
      </kml>`;
  
  const placemarks = messages.map(msg => {
    const timestamp = new Date(msg.satelliteTimestamp).toISOString();
    return `
      <Placemark>
        <name>${msg.imei} - ${msg.status}</name>
        <description><![CDATA[
          Data: ${timestamp}<br/>
          Lat: ${msg.latitude}<br/>
          Lng: ${msg.longitude}<br/>
          Velocità: ${msg.speed} km/h<br/>
          Direzione: ${msg.direction}°<br/>
          Batteria: ${msg.batteryPercentage}%<br/>
          Stato: ${msg.status}
        ]]></description>
        <Point><coordinates>${msg.longitude},${msg.latitude},0</coordinates></Point>
      </Placemark>
    `;
  }).join('\n');
  
  return `${kmlHeader}${placemarks}${kmlFooter}`;
}
