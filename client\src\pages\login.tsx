import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Satellite, Plus, X } from "lucide-react";
import logoEcotrac from "@/assets/logo-ecotrac.png";
import InstallPrompt from "@/components/InstallPrompt";
import { User } from "@/hooks/use-auth";
import { AUTH_CONFIG } from "@/config/auth";

interface LoginPageProps {
  onLogin: (user: User) => void;
}

export default function LoginPage({ onLogin }: LoginPageProps) {
  // Login states
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  // Registration states
  const [regUsername, setRegUsername] = useState("");
  const [regPassword, setRegPassword] = useState("");
  const [imeis, setImeis] = useState<string[]>([""]);
  const [isRegistering, setIsRegistering] = useState(false);
  
  const { toast } = useToast();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username.trim() || !password.trim()) {
      toast({
        title: "Errore",
        description: "Inserisci sia username che password",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await apiRequest('POST', '/api/auth/login', {
        username: username.trim(),
        password: password.trim(),
      });
      
      const data = await response.json();
      
      if (data.user) {
        toast({
          title: "Successo",
          description: "Accesso effettuato con successo",
        });
        onLogin(data.user);
      }
    } catch (error) {
      console.error('Errore di accesso:', error);
      toast({
        title: "Accesso Fallito",
        description: error instanceof Error ? error.message : "Credenziali non valide. Riprova.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!regUsername.trim() || !regPassword.trim()) {
      toast({
        title: "Errore",
        description: "Inserisci sia username che password",
        variant: "destructive",
      });
      return;
    }

    const validImeis = imeis.filter(imei => imei.trim().length > 0);
    if (validImeis.length === 0) {
      toast({
        title: "Errore",
        description: "Inserisci almeno un IMEI",
        variant: "destructive",
      });
      return;
    }

    setIsRegistering(true);
    
    try {
      const response = await apiRequest('POST', '/api/auth/register', {
        username: regUsername.trim(),
        password: regPassword.trim(),
        imeis: validImeis,
      });
      
      const data = await response.json();
      
      if (data.user) {
        toast({
          title: "Successo",
          description: "Registrazione completata con successo",
        });
        onLogin(data.user);
      }
    } catch (error) {
      console.error('Errore di registrazione:', error);
      toast({
        title: "Registrazione Fallita",
        description: error instanceof Error ? error.message : "Registrazione fallita. Riprova.",
        variant: "destructive",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  const addImeiField = () => {
    setImeis([...imeis, ""]);
  };

  const removeImeiField = (index: number) => {
    if (imeis.length > 1) {
      setImeis(imeis.filter((_, i) => i !== index));
    }
  };

  const updateImei = (index: number, value: string) => {
    const newImeis = [...imeis];
    newImeis[index] = value;
    setImeis(newImeis);
  };

  return (
    <div className="min-h-screen flex items-center justify-center" style={{ background: 'linear-gradient(90deg, #005c53 0%, #26a69a 100%)' }}>
      <InstallPrompt />
      <Card className="w-[500px] shadow-xl">
        <CardContent className="p-8">
          <div className="flex flex-col items-center mb-6">
            <img src={logoEcotrac} alt="Logo Ecotrac" style={{ width: 64, height: 64, marginBottom: 8 }} />
            <h2 className="text-3xl font-bold text-gray-800 tracking-tight" style={{ letterSpacing: 1 }}>ECOTrac</h2>
            {typeof window !== 'undefined' && localStorage.getItem(AUTH_CONFIG.STORAGE_KEYS.USER) && JSON.parse(localStorage.getItem(AUTH_CONFIG.STORAGE_KEYS.USER)!).role === 'admin' && (
              <span className="mt-2 px-3 py-1 bg-yellow-400 text-xs rounded-full font-semibold text-gray-800">Admin</span>
            )}
          </div>
          
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="login">Accesso</TabsTrigger>
              <TabsTrigger value="register">Registrazione</TabsTrigger>
            </TabsList>
            
            <TabsContent value="login" className="space-y-4 mt-6">
              <form onSubmit={handleLogin} className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2">Nome Utente</Label>
                  <Input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Inserisci nome utente"
                    disabled={isLoading}
                    className="focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2">Password</Label>
                  <Input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Inserisci password"
                    disabled={isLoading}
                    className="focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
                
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-primary text-white hover:bg-blue-700 transition-colors font-medium"
                >
                  {isLoading ? "Accesso in corso..." : "Accedi"}
                </Button>
              </form>
            </TabsContent>
            
            <TabsContent value="register" className="space-y-4 mt-6">
              <form onSubmit={handleRegister} className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2">Nome Utente</Label>
                  <Input
                    type="text"
                    value={regUsername}
                    onChange={(e) => setRegUsername(e.target.value)}
                    placeholder="Inserisci nome utente"
                    disabled={isRegistering}
                    className="focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2">Password</Label>
                  <Input
                    type="password"
                    value={regPassword}
                    onChange={(e) => setRegPassword(e.target.value)}
                    placeholder="Inserisci password"
                    disabled={isRegistering}
                    className="focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label className="text-sm font-medium text-gray-700">IMEI</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addImeiField}
                      className="h-8 px-2"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {imeis.map((imei, index) => (
                    <div key={index} className="flex items-center space-x-2 mb-2">
                      <Input
                        type="text"
                        value={imei}
                        onChange={(e) => updateImei(index, e.target.value)}
                        placeholder="Inserisci IMEI"
                        disabled={isRegistering}
                        className="flex-1"
                      />
                      {imeis.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeImeiField(index)}
                          className="h-10 px-2"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
                
                <Button
                  type="submit"
                  disabled={isRegistering}
                  className="w-full bg-primary text-white hover:bg-blue-700 transition-colors font-medium"
                >
                  {isRegistering ? "Registrazione in corso..." : "Registrati"}
                </Button>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
