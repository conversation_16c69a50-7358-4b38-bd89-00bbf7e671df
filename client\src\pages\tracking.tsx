import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Satellite, LogOut, Bell, BellOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import MapSection from "@/components/map-section";
import DataTableSection from "@/components/data-table-section";
import { useSatelliteData } from "@/hooks/use-satellite-data";
import { Resizable } from "@/components/ui/resizable";
import type { Trip } from "@shared/schema";
import logoEcotrac from '../assets/logo-ecotrac.png';
import { useLocation } from "wouter";
import { User } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";

interface TrackingPageProps {
  user: User;
  onLogout: () => void;
}

export default function TrackingPage({ user, onLogout }: TrackingPageProps) {
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  const [selectedImei, setSelectedImei] = useState<string>(() => {
    // Per gli admin, inizia con stringa vuota e sarà impostato quando i dati sono disponibili
    // Per gli utenti normali, usa il primo IMEI disponibile
    return user.role === 'admin' ? "" : (user.imeis[0] || "");
  });
  const [selectedPosition, setSelectedPosition] = useState<{ lat: number; lng: number } | null>(null);
  const [selectedMessageId, setSelectedMessageId] = useState<number | null>(null);
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<{ start: Date | null; end: Date | null }>(() => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Set today to 00:00:00
    today.setHours(0, 0, 0, 0);
    // Set tomorrow to 00:00:00
    tomorrow.setHours(0, 0, 0, 0);
    
    return {
      start: today,
      end: tomorrow,
    };
  });
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission>('default');
  const { toast } = useToast();

  // Fetch trips for user's IMEIs
  const { data: trips = [], isLoading: isLoadingTrips } = useQuery<Trip[]>({
    queryKey: ['/api/trips', user.id],
    enabled: !!user.id,
    queryFn: async () => {
      const response = await fetch(`/api/trips?userId=${user.id}`);
      if (!response.ok) throw new Error('Failed to fetch trips');
      return response.json();
    },
  });

  // Fetch all IMEIs for admin users
  const { data: allImeis = [], isLoading: isLoadingImeis } = useQuery<{imei: string; username: string; displayText: string}[]>({
    queryKey: ['/api/admin/imeis'],
    enabled: user.role === 'admin',
    queryFn: async () => {
      console.log('Fetching admin IMEIs...');
      const response = await fetch('/api/admin/imeis');
      if (!response.ok) throw new Error('Failed to fetch admin IMEIs');
      const data = await response.json();
      console.log('Fetched admin IMEIs:', data);
      return data;
    },
  });

  // Debug: monitora i cambiamenti degli IMEI
  useEffect(() => {
    console.log('allImeis changed:', allImeis);
  }, [allImeis]);

  // Imposta il primo IMEI disponibile per gli admin quando i dati sono caricati
  useEffect(() => {
    if (user.role === 'admin' && allImeis.length > 0 && !selectedImei) {
      console.log('Setting first IMEI:', allImeis[0].imei);
      setSelectedImei(allImeis[0].imei);
    }
  }, [user.role, allImeis, selectedImei]);

  // L'aggiornamento automatico dei viaggi è ora gestito nel hook useSatelliteData

  // Fetch messages for specific trip or all messages
  const { data: tripMessages = [], isLoading: isLoadingTripMessages } = useQuery({
    queryKey: ['/api/trips', selectedTripId, 'messages', user.id],
    enabled: !!selectedTripId && !!user.id,
    queryFn: async () => {
      const response = await fetch(`/api/trips/${selectedTripId}/messages?userId=${user.id}`);
      if (!response.ok) throw new Error('Failed to fetch trip messages');
      return response.json();
    },
  });

  const { messages: allMessages, onlineDevices, isLoading } = useSatelliteData(
    user.id,
    selectedImei,
    dateRange.start,
    dateRange.end
  );

  // Use trip messages if a trip is selected, otherwise use all messages
  const messages = selectedTripId ? tripMessages : allMessages;
  const isLoadingMessages = selectedTripId ? isLoadingTripMessages : isLoading;

  // Carica stato notifiche dal localStorage
  useEffect(() => {
    const savedNotifications = localStorage.getItem(`notifications_${user.id}`);
    if (savedNotifications) {
      setNotificationsEnabled(JSON.parse(savedNotifications));
    }
    
    // Controlla permesso notifiche del browser
    if ('Notification' in window) {
      setNotificationPermission(Notification.permission);
    }
  }, [user.id]);

  // Gestisce il toggle delle notifiche
  const handleNotificationToggle = async () => {
    if (!('Notification' in window)) {
      toast({
        title: "Notifiche non supportate",
        description: "Il tuo browser non supporta le notifiche",
        variant: "destructive",
      });
      return;
    }

    if (notificationPermission === 'denied') {
      toast({
        title: "Notifiche bloccate",
        description: "Le notifiche sono state bloccate. Abilitale nelle impostazioni del browser.",
        variant: "destructive",
      });
      return;
    }

    if (!notificationsEnabled && notificationPermission === 'default') {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);
      
      if (permission === 'denied') {
        toast({
          title: "Permesso negato",
          description: "Permesso per le notifiche negato",
          variant: "destructive",
        });
        return;
      }
    }

    const newState = !notificationsEnabled;
    setNotificationsEnabled(newState);
    localStorage.setItem(`notifications_${user.id}`, JSON.stringify(newState));

    toast({
      title: newState ? "Notifiche attivate" : "Notifiche disattivate",
      description: newState 
        ? "Riceverai notifiche per: inizio/fine viaggio e batteria bassa" 
        : "Non riceverai più notifiche",
    });

    // Notifica di test se abilitata
    if (newState && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('ECOTrac - Notifiche attivate', {
        body: 'Riceverai aggiornamenti per i tuoi dispositivi satellitari',
        icon: '/assets/logo-ecotrac.png',
      });
    }
  };

  const handleMarkerClick = (messageId: number, lat: number, lng: number) => {
    setSelectedPosition({ lat, lng });
    setSelectedMessageId(messageId);
  };

  const handleTableRowClick = (messageId: number, lat: number, lng: number) => {
    setSelectedPosition({ lat, lng });
    setSelectedMessageId(messageId);
  };

  const handleImeiChange = (newImei: string) => {
    // Clear current selection when changing IMEI
    setSelectedPosition(null);
    setSelectedMessageId(null);
    setSelectedTripId(null);
    setSelectedImei(newImei);
    
    // Invalida la query dei viaggi per caricare viaggi aggiornati per il nuovo IMEI
    queryClient.invalidateQueries({ queryKey: ['/api/trips', user.id] });
  };

  const handleTripChange = (tripId: string | null) => {
    // Clear current selection when changing trip
    setSelectedPosition(null);
    setSelectedMessageId(null);
    setSelectedTripId(tripId);
  };

  const handleDateRangeChange = (range: { start: Date | null; end: Date | null }) => {
    setDateRange(range);
    // Forza il refetch della query dei messaggi
    queryClient.invalidateQueries({ queryKey: ['/api/messages', user.id, selectedImei, range.start?.toISOString(), range.end?.toISOString()] });
    // Forza anche il refetch della query dei viaggi per aggiornare la lista dei viaggi
    queryClient.invalidateQueries({ queryKey: ['/api/trips', user.id] });
  };

  const filteredMessages = selectedImei 
    ? messages.filter((msg: any) => msg.imei === selectedImei)
    : messages;

  // Filter trips by selected IMEI (for admin users who can see all trips)
  const filteredTrips = selectedImei 
    ? trips.filter((trip: any) => trip.imei === selectedImei)
    : trips;

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex flex-col h-screen overflow-hidden">
        {/* Header */}
        <header className="bg-gradient-to-r from-[#005c53] to-[#26a69a] shadow-sm border-b border-gray-200 flex-shrink-0">
          <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <img src={logoEcotrac} alt="ECOTrac logo" className="h-12 w-12 mr-3" />
                <h1 className="text-4xl font-bold text-white">ECOTrac</h1>
                <div className="ml-8 flex items-center space-x-4">
                  <div className="flex items-center">
                    <span className="text-sm text-gray-100 mr-2">IMEI:</span>
                    <Select
                      value={selectedImei}
                      onValueChange={handleImeiChange}
                    >
                      <SelectTrigger className="w-64 text-sm">
                        <SelectValue placeholder="Seleziona IMEI" className="text-gray-900" />
                      </SelectTrigger>
                      <SelectContent className="z-50">
                        {user.role === 'admin' ? (
                          // Admin: mostra tutti gli IMEI con formato username - XXXX
                          allImeis.map((imeiData) => (
                            <SelectItem key={imeiData.imei} value={imeiData.imei}>
                              {imeiData.displayText}
                            </SelectItem>
                          ))
                        ) : (
                          // User normale: mostra solo i propri IMEI
                          user.imeis.map((imei) => (
                            <SelectItem key={imei} value={imei}>
                              {imei}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center">
                    {selectedImei ? (
                      <>
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          onlineDevices.includes(selectedImei) ? 'bg-green-400' : 'bg-red-400'
                        }`}></div>
                        <span className="text-sm text-gray-100">
                          {onlineDevices.includes(selectedImei) ? 'Connesso' : 'Non Connesso'}
                        </span>
                      </>
                    ) : (
                      <>
                        <div className="w-2 h-2 bg-gray-300 rounded-full mr-2"></div>
                        <span className="text-sm text-gray-100">
                          Seleziona IMEI
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Toggle Notifiche */}
                <Button 
                  variant="ghost" 
                  className="text-white hover:bg-white/20 flex items-center space-x-1" 
                  onClick={handleNotificationToggle}
                  title={notificationsEnabled ? "Disattiva notifiche" : "Attiva notifiche"}
                >
                  {notificationsEnabled ? (
                    <Bell className="h-4 w-4" />
                  ) : (
                    <BellOff className="h-4 w-4" />
                  )}
                  <span className="text-sm hidden sm:inline">
                    {notificationsEnabled ? "ON" : "OFF"}
                  </span>
                </Button>

                {user.role === 'admin' && (
                  <Button variant="outline" className="bg-white text-[#005c53] hover:bg-gray-100 border-white" onClick={() => navigate('/admin')}>
                    Admin
                  </Button>
                )}
                <Button variant="ghost" className="text-white hover:bg-white/20" onClick={onLogout}>
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* Map Section - Resizable */}
          <Resizable
            className="flex-shrink-0"
            minHeight={300}
            maxHeight={800}
            defaultHeight={400}
          >
            <MapSection
              key={`${selectedImei}-${selectedTripId || 'all'}`}
              messages={filteredMessages}
              trips={filteredTrips}
              selectedPosition={selectedPosition}
              selectedMessageId={selectedMessageId}
              onMarkerClick={handleMarkerClick}
              isLoading={isLoading}
            />
          </Resizable>

          {/* Data Table Section - Remaining space */}
          <div className="flex-1 min-h-0">
            <DataTableSection
              messages={filteredMessages}
              selectedMessageId={selectedMessageId}
              dateRange={dateRange}
              onDateRangeChange={handleDateRangeChange}
              onRowClick={handleTableRowClick}
              isLoading={isLoadingMessages}
              trips={filteredTrips}
              selectedTripId={selectedTripId}
              onTripChange={handleTripChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
