import { useState, useEffect, useCallback } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { websocketManager, handleInAppNotification } from "@/lib/websocket";
import type { SatelliteMessage, WebSocketMessage } from "@shared/schema";

export function useSatelliteData(
  userId: number,
  selectedImei?: string,
  startDate?: Date | null,
  endDate?: Date | null
) {
  const [realtimeMessages, setRealtimeMessages] = useState<SatelliteMessage[]>([]);
  const [onlineDevices, setOnlineDevices] = useState<string[]>([]);
  const queryClient = useQueryClient();

  // Fetch initial messages
  const { data: initialMessages = [], isLoading: isLoadingMessages } = useQuery({
    queryKey: ['/api/messages', userId, selectedImei, startDate?.toISOString(), endDate?.toISOString()],
    enabled: !!userId,
    staleTime: 30000, // Consider data stale after 30 seconds
    gcTime: 60000, // Keep in cache for 1 minute
    queryFn: async () => {
      console.log('[useSatelliteData] FETCH /api/messages', { userId, selectedImei, startDate, endDate });
      const params = new URLSearchParams();
      params.append('userId', userId.toString());
      if (selectedImei) params.append('imei', selectedImei);
      if (startDate) params.append('startDate', startDate.toISOString());
      if (endDate) params.append('endDate', endDate.toISOString());
      
      const response = await fetch(`/api/messages?${params.toString()}`);
      if (!response.ok) throw new Error('Failed to fetch messages');
      return response.json();
    },
  });

  // Fetch online devices status
  const { data: devicesData, isLoading: isLoadingDevices } = useQuery({
    queryKey: ['/api/devices/online', userId],
    enabled: !!userId,
    refetchInterval: false, // Only update via WebSocket
    queryFn: async () => {
      const response = await fetch(`/api/devices/online?userId=${userId}`);
      if (!response.ok) throw new Error('Failed to fetch devices');
      return response.json();
    },
  });

  useEffect(() => {
    if (devicesData?.onlineDevices) {
      setOnlineDevices(devicesData.onlineDevices);
    }
  }, [devicesData]);

  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    if (message.type === 'new_message') {
      const newMessage = message.data;
      
      console.log('[useSatelliteData] Nuovo messaggio WebSocket:', newMessage);
      console.log('[useSatelliteData] selectedImei:', selectedImei);
      
      // Filter WebSocket messages by selectedImei if specified
      if (selectedImei && newMessage.imei !== selectedImei) {
        console.log('[useSatelliteData] Messaggio filtrato - IMEI non corrispondente:', newMessage.imei, '!==', selectedImei);
        
        // Still invalidate trips query for Start/End messages even if filtered
        if (newMessage.status === 'Start' || newMessage.status === 'End') {
          console.log('[useSatelliteData] Invalidating trips query due to Start/End message (filtered)');
          queryClient.invalidateQueries({ queryKey: ['/api/trips'] });
        }
        return;
      }
      
      console.log('[useSatelliteData] Messaggio WebSocket accettato per IMEI:', newMessage.imei);
      
      // Add message to realtime messages
      setRealtimeMessages(prev => {
        // Check if message already exists
        const exists = prev.some(msg => msg.id === newMessage.id);
        if (exists) {
          console.log('[useSatelliteData] Messaggio già esistente, ignorato:', newMessage.id);
          return prev;
        }
        
        // Add new message and sort by timestamp
        const updated = [...prev, newMessage];
        const sorted = updated.sort((a, b) => 
          new Date(b.satelliteTimestamp).getTime() - new Date(a.satelliteTimestamp).getTime()
        );
        console.log('[useSatelliteData] Aggiunto nuovo messaggio, totale realtime:', sorted.length);
        return sorted;
      });

      // Controlla se il messaggio dovrebbe generare una notifica
      const notificationsEnabled = localStorage.getItem(`notifications_${userId}`);
      if (notificationsEnabled === 'true') {
        // Genera notifiche per eventi specifici
        if (newMessage.status === 'Start') {
          handleInAppNotification({
            title: '🚀 Viaggio iniziato',
            body: `Dispositivo: ${newMessage.imei}`
          });
        } else if (newMessage.status === 'End') {
          handleInAppNotification({
            title: '🏁 Viaggio terminato', 
            body: `Dispositivo: ${newMessage.imei}`
          });
        } else if (newMessage.batteryPercentage !== undefined && newMessage.batteryPercentage < 15) {
          handleInAppNotification({
            title: '🔋 Batteria bassa',
            body: `Dispositivo: ${newMessage.imei} \n Batteria: ${newMessage.batteryPercentage}%`
          });
        }
      }

      // Invalidate messages query to ensure fresh data when switching back to this IMEI
      console.log('[useSatelliteData] Invalidating messages query due to new message for current IMEI');
      queryClient.invalidateQueries({ queryKey: ['/api/messages'] });

      // If it's a Start or End message, invalidate trips query to refresh trip list
      if (newMessage.status === 'Start' || newMessage.status === 'End') {
        console.log('[useSatelliteData] Invalidating trips query due to Start/End message');
        queryClient.invalidateQueries({ queryKey: ['/api/trips'] });
      }
    } else if (message.type === 'device_status') {
      const { imei, online } = message.data;
      setOnlineDevices(prev => {
        if (online) {
          return prev.includes(imei) ? prev : [...prev, imei];
        } else {
          return prev.filter(device => device !== imei);
        }
      });
    }
  }, [queryClient, selectedImei]);

  // Set up WebSocket connection
  useEffect(() => {
    websocketManager.connect();
    const unsubscribe = websocketManager.onMessage(handleWebSocketMessage);

    return () => {
      unsubscribe();
    };
  }, [handleWebSocketMessage]);

  // Reset realtime messages only when userId, startDate, or endDate change
  // DO NOT reset when selectedImei changes - filter client-side instead
  useEffect(() => {
    console.log('[useSatelliteData] Resetting realtime messages due to filter change');
    setRealtimeMessages([]);
  }, [userId, startDate, endDate]);

  // Reset realtime messages when selectedImei changes to avoid stale filtered data
  useEffect(() => {
    console.log('[useSatelliteData] Resetting realtime messages due to IMEI change:', selectedImei);
    setRealtimeMessages([]);
  }, [selectedImei]);

  // Combine initial messages with realtime messages, removing duplicates
  const allMessages = [...initialMessages, ...realtimeMessages].reduce((acc, message) => {
    const existing = acc.find((msg: SatelliteMessage) => msg.id === message.id);
    if (!existing) {
      acc.push(message);
    }
    return acc;
  }, [] as SatelliteMessage[]);

  // Filter messages by date range if they exist (for realtime messages that might be outside the range)
  const filteredMessages = allMessages.filter((message: SatelliteMessage) => {
    const messageTime = new Date(message.satelliteTimestamp);
    
    if (startDate && messageTime < startDate) return false;
    if (endDate && messageTime > endDate) return false;
    
    return true;
  });

  return {
    messages: filteredMessages,
    onlineDevices,
    isLoading: isLoadingMessages || isLoadingDevices,
    isConnected: websocketManager.isConnected(),
  };
}
