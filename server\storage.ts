import { type User, type InsertUser, type SatelliteMessage, type InsertSatelliteMessage, type Trip } from "@shared/schema";

export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Satellite message management
  addSatelliteMessage(message: InsertSatelliteMessage): Promise<SatelliteMessage>;
  getSatelliteMessages(imeis: string[], startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]>;
  getSatelliteMessagesByImei(imei: string, startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]>;
  
  // Trip management
  getTrips(imeis: string[]): Promise<Trip[]>;
  getTripMessages(tripId: string): Promise<SatelliteMessage[]>;
  reorganizeTripAssignments(imei?: string): Promise<void>;
  
  // Device status tracking
  getOnlineDevices(): Promise<string[]>;
  setDeviceOnline(imei: string): Promise<void>;
  setDeviceOffline(imei: string): Promise<void>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private userIdCounter: number;
  private messages: Map<number, SatelliteMessage>;
  private messageIdCounter: number;
  private onlineDevices: Set<string>;

  constructor() {
    this.users = new Map();
    this.userIdCounter = 1;
    this.messages = new Map();
    this.messageIdCounter = 1;
    this.onlineDevices = new Set();
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const user: User = { ...insertUser, id, role: insertUser.role || 'user' };
    this.users.set(id, user);
    return user;
  }

  async addSatelliteMessage(insertMessage: InsertSatelliteMessage): Promise<SatelliteMessage> {
    const id = this.messageIdCounter++;
    
    console.log(`Processing message: ${insertMessage.status} for IMEI: ${insertMessage.imei}`);
    
    // Generate tripId for Start/End messages
    let tripId: string | null = null;
    if (insertMessage.status === 'Start') {
      tripId = `trip_${insertMessage.imei}_${Date.now()}`;
      console.log(`Created new tripId: ${tripId}`);
    } else if (insertMessage.status === 'End') {
      // Find the most recent Start message for this IMEI without an End
      const messages = Array.from(this.messages.values())
        .filter(m => m.imei === insertMessage.imei && m.status === 'Start' && m.tripId)
        .sort((a, b) => new Date(b.satelliteTimestamp).getTime() - new Date(a.satelliteTimestamp).getTime());
      
      console.log(`Found ${messages.length} Start messages for IMEI ${insertMessage.imei}`);
      
      if (messages.length > 0) {
        tripId = messages[0].tripId;
        console.log(`Assigned tripId: ${tripId} to End message`);
      } else {
        console.log(`No Start message found for IMEI ${insertMessage.imei}`);
      }
    } else {
      // Per messaggi Fixed/No Fix, trova il viaggio cronologicamente corretto
      const messageTime = typeof insertMessage.satelliteTimestamp === 'string' 
        ? new Date(insertMessage.satelliteTimestamp) 
        : insertMessage.satelliteTimestamp;
      tripId = this.findChronologicallyCorrectTrip(insertMessage.imei, messageTime);
    }
    
    const message: SatelliteMessage = {
      ...insertMessage,
      id,
      serverTimestamp: new Date(),
      tripId,
    };
    this.messages.set(id, message);
    
    console.log(`Saved message with ID: ${id}, tripId: ${tripId}, status: ${insertMessage.status}`);
    return message;
  }

  /**
   * Trova il viaggio cronologicamente corretto per un messaggio
   */
  private findChronologicallyCorrectTrip(imei: string, messageTime: Date): string | null {
    const startMessages = Array.from(this.messages.values())
      .filter(m => m.imei === imei && m.status === 'Start' && m.tripId)
      .sort((a, b) => {
        const aTime = typeof a.satelliteTimestamp === 'string' ? new Date(a.satelliteTimestamp) : a.satelliteTimestamp;
        const bTime = typeof b.satelliteTimestamp === 'string' ? new Date(b.satelliteTimestamp) : b.satelliteTimestamp;
        return aTime.getTime() - bTime.getTime(); // Ordine cronologico crescente
      });
    
    console.log(`Found ${startMessages.length} Start messages for IMEI ${imei}`);
    
    // Trova il viaggio attivo che contiene temporalmente questo messaggio
    for (const startMsg of startMessages) {
      const endMsg = Array.from(this.messages.values())
        .find(m => m.imei === imei && m.status === 'End' && m.tripId === startMsg.tripId);
      
      const tripStart = typeof startMsg.satelliteTimestamp === 'string' 
        ? new Date(startMsg.satelliteTimestamp) 
        : startMsg.satelliteTimestamp;
      const tripEnd = endMsg 
        ? (typeof endMsg.satelliteTimestamp === 'string' ? new Date(endMsg.satelliteTimestamp) : endMsg.satelliteTimestamp)
        : new Date(); // Se non c'è End, il viaggio è ancora attivo
      
      console.log(`Checking trip ${startMsg.tripId}: ${tripStart.toISOString()} - ${tripEnd.toISOString()}, message time: ${messageTime.toISOString()}`);
      
      if (messageTime >= tripStart && messageTime <= tripEnd) {
        console.log(`Message assigned to trip ${startMsg.tripId} (chronologically correct)`);
        return startMsg.tripId;
      }
    }
    
    console.log(`No active trip found for message at ${messageTime.toISOString()}`);
    return null;
  }

  /**
   * Riorganizza i messaggi esistenti per assegnare correttamente i tripId
   * Utile per correggere dati già importati
   */
  async reorganizeTripAssignments(imei?: string): Promise<void> {
    const messagesToProcess = Array.from(this.messages.values())
      .filter(m => !imei || m.imei === imei)
      .sort((a, b) => {
        const aTime = typeof a.satelliteTimestamp === 'string' ? new Date(a.satelliteTimestamp) : a.satelliteTimestamp;
        const bTime = typeof b.satelliteTimestamp === 'string' ? new Date(b.satelliteTimestamp) : b.satelliteTimestamp;
        return aTime.getTime() - bTime.getTime();
      });
    
    console.log(`Reorganizing ${messagesToProcess.length} messages for ${imei || 'all IMEIs'}`);
    
    // Rimuovi tutti i tripId dai messaggi Fixed/No Fix
    for (const message of messagesToProcess) {
      if (message.status === 'Fixed' || message.status === 'No Fix') {
        message.tripId = null;
      }
    }
    
    // Riassegna i tripId cronologicamente
    for (const message of messagesToProcess) {
      if (message.status === 'Fixed' || message.status === 'No Fix') {
        const messageTime = typeof message.satelliteTimestamp === 'string' 
          ? new Date(message.satelliteTimestamp) 
          : message.satelliteTimestamp;
        const correctTripId = this.findChronologicallyCorrectTrip(message.imei, messageTime);
        message.tripId = correctTripId;
      }
    }
    
    console.log('Trip reorganization completed');
  }

  async getSatelliteMessages(imeis: string[], startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]> {
    const messages = Array.from(this.messages.values());
    console.log('[getSatelliteMessages] startDate:', startDate ? startDate.toISOString() : null, 'endDate:', endDate ? endDate.toISOString() : null);
    const filtered = messages.filter(message => {
      if (!imeis.includes(message.imei)) return false;

      // Conversione robusta
      if (typeof message.satelliteTimestamp === 'string') {
        message.satelliteTimestamp = new Date(message.satelliteTimestamp);
      }

      // Log confronto per ogni messaggio
      console.log('[getSatelliteMessages] IMEI:', message.imei, 'satelliteTimestamp:', message.satelliteTimestamp instanceof Date ? message.satelliteTimestamp.toISOString() : message.satelliteTimestamp, 'startDate:', startDate ? startDate.toISOString() : null, 'endDate:', endDate ? endDate.toISOString() : null);

      if (startDate && message.satelliteTimestamp < startDate) return false;
      if (endDate && message.satelliteTimestamp > endDate) return false;

      return true;
    }).sort((a, b) => {
      // Conversione robusta anche qui
      const aTime = typeof a.satelliteTimestamp === 'string' ? new Date(a.satelliteTimestamp) : a.satelliteTimestamp;
      const bTime = typeof b.satelliteTimestamp === 'string' ? new Date(b.satelliteTimestamp) : b.satelliteTimestamp;
      return bTime.getTime() - aTime.getTime();
    });
    console.log(`[getSatelliteMessages] Messaggi restituiti al frontend: ${filtered.length}`);
    return filtered;
  }

  async getSatelliteMessagesByImei(imei: string, startDate?: Date, endDate?: Date): Promise<SatelliteMessage[]> {
    return this.getSatelliteMessages([imei], startDate, endDate);
  }

  async getOnlineDevices(): Promise<string[]> {
    return Array.from(this.onlineDevices);
  }

  async setDeviceOnline(imei: string): Promise<void> {
    this.onlineDevices.add(imei);
  }

  async setDeviceOffline(imei: string): Promise<void> {
    this.onlineDevices.delete(imei);
  }

  async getTrips(imeis: string[]): Promise<Trip[]> {
    const messages = Array.from(this.messages.values());
    console.log(`Total messages in storage: ${messages.length}`);
    console.log(`Looking for trips for IMEIs: ${imeis.join(', ')}`);
    
    const startMessages = messages.filter(m => 
      imeis.includes(m.imei) && m.status === 'Start' && m.tripId
    );
    
    console.log(`Found ${startMessages.length} Start messages with tripId`);

    const trips: Trip[] = [];
    
    for (const startMsg of startMessages) {
      if (!startMsg.tripId) continue;
      
      const endMsg = messages.find(m => 
        m.imei === startMsg.imei && 
        m.status === 'End' && 
        m.tripId === startMsg.tripId
      );

      console.log(`Trip ${startMsg.tripId}: Start at ${startMsg.satelliteTimestamp}, End: ${endMsg ? endMsg.satelliteTimestamp : 'Not found'}`);

      trips.push({
        id: startMsg.tripId,
        imei: startMsg.imei,
        startTime: startMsg.satelliteTimestamp,
        endTime: endMsg ? endMsg.satelliteTimestamp : null,
        startMessage: startMsg,
        endMessage: endMsg,
      });
    }

    console.log(`Returning ${trips.length} trips`);
    return trips.sort((a, b) => 
      new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    );
  }

  async getTripMessages(tripId: string): Promise<SatelliteMessage[]> {
    const messages = Array.from(this.messages.values());
    return messages
      .filter(m => m.tripId === tripId)
      .sort((a, b) => 
        new Date(a.satelliteTimestamp).getTime() - new Date(b.satelliteTimestamp).getTime()
      );
  }


}

export const storage = new MemStorage();

