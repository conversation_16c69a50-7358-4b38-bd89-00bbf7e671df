# Deployment Guide

This guide explains how to deploy the Satellite Tracker application on both Windows and Debian/Linux systems.

## Prerequisites

- Node.js (version 18 or higher)
- npm (comes with Node.js)

## Development Mode

To run the application in development mode (with hot reload):

```bash
npm run dev
```

The application will be available at `http://localhost:5000`

## Production Deployment

### Option 1: Cross-Platform npm Scripts (Recommended)

These commands work on both Windows and Linux:

```bash
# Clean build and deploy
npm run deploy

# Or step by step:
npm run build:clean
npm run start:prod
```

### Option 2: Platform-Specific Scripts

#### On Windows:
```bash
npm run deploy
```

#### On Debian/Linux:

**Option A: Direct deployment**
```bash
# Make the script executable (first time only)
chmod +x deploy.sh

# Run the deployment
./deploy.sh
```

**Option B: Using PM2 (Recommended for production)**
```bash
# Install PM2 globally (first time only)
npm install -g pm2

# Build the application
npm run build:clean

# Start with PM2
pm2 start ecosystem.config.js --env production

# Other PM2 commands
pm2 status          # Check status
pm2 logs            # View logs
pm2 restart all     # Restart
pm2 stop all        # Stop
pm2 delete all      # Delete processes
```

## Manual Deployment Steps

If you prefer to run the steps manually:

1. **Clean previous build:**
   ```bash
   # Windows
   rmdir /s /q dist

   # Linux
   rm -rf dist
   ```

2. **Build the application:**
   ```bash
   npm run build
   ```

3. **Start production server:**
   ```bash
   npm run start:prod
   ```

## Troubleshooting

### Cache Issues

If you encounter JavaScript module loading errors (MIME type issues), run:

```bash
npm run build:clean
npm run start:prod
```

This will force a complete rebuild with new file hashes.

### Port Configuration

The application runs on port 5000 by default. Make sure this port is available and not blocked by firewall.

### Environment Variables

The application automatically detects the environment:
- Development: `NODE_ENV=development`
- Production: `NODE_ENV=production`

## File Structure After Build

```
dist/
├── index.js              # Server bundle
└── public/
    ├── index.html         # Main HTML file
    ├── manifest.json      # PWA manifest
    ├── service-worker.js  # Service worker
    └── assets/
        ├── index-[hash].js   # Client JavaScript bundle
        ├── index-[hash].css  # Styles
        └── logo-[hash].png   # Assets
```

## Platform-Specific Notes

### Windows
- Uses PowerShell commands where needed
- Supports both Command Prompt and PowerShell
- Cross-platform Node.js scripts are preferred

### Debian/Linux
- Uses bash shell scripts
- Requires executable permissions for shell scripts
- Supports systemd for service management (if needed)

## Quick Start Commands

### Windows Development
```bash
npm run dev
```

### Windows Production
```bash
npm run deploy
```

### Linux/Debian Production (Simple)
```bash
chmod +x deploy.sh
./deploy.sh
```

### Linux/Debian Production (PM2)
```bash
npm run build:clean
npm run pm2:start
```

## Production Considerations

1. **Reverse Proxy**: Consider using nginx or Apache as a reverse proxy
2. **Process Management**: Use PM2, systemd, or similar for process management
3. **SSL/TLS**: Configure HTTPS in production
4. **Monitoring**: Set up logging and monitoring
5. **Backups**: Regular backup of data files

## Cache Issue Resolution

If you encounter JavaScript module loading errors after deployment:

1. **Force clean rebuild:**
   ```bash
   npm run build:clean
   ```

2. **Clear browser cache** (Ctrl+F5 or Cmd+Shift+R)

3. **The application includes automatic cache-busting** that should prevent most issues

## Available npm Scripts

- `npm run dev` - Development mode with hot reload
- `npm run build` - Build for production
- `npm run build:clean` - Clean build (removes cache)
- `npm run start:prod` - Start production server
- `npm run deploy` - Full deployment (clean + build + start)
- `npm run pm2:start` - Start with PM2
- `npm run pm2:stop` - Stop PM2 processes
- `npm run pm2:restart` - Restart PM2 processes
- `npm run pm2:logs` - View PM2 logs
- `npm run pm2:status` - Check PM2 status
