import fs from 'fs/promises';
import path from 'path';
import { parse, stringify } from 'csv-string';

export interface CSVUser {
  username: string;
  password: string;
  imeis: string; // comma-separated IMEI list
  role: string; // "admin" o "user"
}

export class UserManager {
  private csvPath: string;

  constructor() {
    this.csvPath = path.join(process.cwd(), 'users.csv');
  }

  private async ensureCSVExists(): Promise<void> {
    try {
      await fs.access(this.csvPath);
    } catch {
      // Create CSV with headers if it doesn't exist
      const headers = 'username,password,imeis,role\n';
      await fs.writeFile(this.csvPath, headers, 'utf8');
    }
  }

  async loadUsers(): Promise<CSVUser[]> {
    await this.ensureCSVExists();
    
    try {
      const csvContent = await fs.readFile(this.csvPath, 'utf8');
      const lines = csvContent.trim().split('\n');
      
      if (lines.length <= 1) return []; // Only headers or empty
      
      const dataLines = lines.slice(1); // Skip header
      
      return dataLines.map(line => {
        const parsed = parse(line)[0];
        const [username, password, imeis, role] = parsed;
        return { 
          username, 
          password, 
          imeis, 
          role: role || 'user'
        };
      });
    } catch (error) {
      console.error('Error loading users from CSV:', error);
      return [];
    }
  }

  async saveUser(user: CSVUser): Promise<void> {
    await this.ensureCSVExists();
    
    const users = await this.loadUsers();
    
    // Check if user already exists
    const existingUserIndex = users.findIndex(u => u.username === user.username);
    
    if (existingUserIndex >= 0) {
      users[existingUserIndex] = user;
    } else {
      users.push(user);
    }
    
    // Write all users back to CSV
    const headers = 'username,password,imeis,role\n';
    const userLines = users.map(u => `${u.username},${u.password},"${u.imeis}",${u.role}`);
    const csvContent = headers + userLines.join('\n') + '\n';
    
    await fs.writeFile(this.csvPath, csvContent, 'utf8');
  }

  async getUserByUsername(username: string): Promise<CSVUser | undefined> {
    const users = await this.loadUsers();
    return users.find(u => u.username === username);
  }

  async validateUser(username: string, password: string): Promise<CSVUser | null> {
    const user = await this.getUserByUsername(username);
    if (user && user.password === password) {
      return user;
    }
    return null;
  }


}

export const userManager = new UserManager();
