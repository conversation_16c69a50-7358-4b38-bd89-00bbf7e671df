#!/bin/bash

echo "Starting Satellite Tracker Application..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
fi

print_success "Node.js found: $(node --version)"

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
    fi
    print_success "Dependencies installed"
fi

# Check if build exists
if [ ! -d "dist" ]; then
    echo "Building application..."
    npm run build
    if [ $? -ne 0 ]; then
        print_error "Build failed"
    fi
    print_success "Application built"
fi

# Detect environment and start accordingly
if [ "$NODE_ENV" = "production" ]; then
    print_success "Starting in PRODUCTION mode..."
    npm run start:prod
else
    print_success "Starting in DEVELOPMENT mode..."
    echo "You can also run: npm run dev"
    npm run start:dev
fi
