# Gestione Timeout Login - ECOTrac

## Panoramica

Il sistema di autenticazione è stato aggiornato per includere un timeout automatico della sessione e il reset del login quando si chiude il browser.

## Funzionalità Implementate

### 1. Timeout Automatico della Sessione
- **Durata predefinita**: 8 ore
- **Controllo periodico**: Ogni minuto il sistema verifica se la sessione è scaduta
- **Logout automatico**: Quando la sessione scade, l'utente viene automaticamente disconnesso

### 2. Reset alla Chiusura del Browser
- **Evento `beforeunload`**: Rimuove i dati di sessione quando si chiude il browser/tab
- **Evento `pagehide`**: Fallback per browser che non supportano `beforeunload`
- **Pulizia completa**: Rimuove sia i dati utente che il timestamp di login

### 3. Configurazione Centralizzata
- **File**: `client/src/config/auth.ts`
- **Facile personalizzazione**: Modifica i valori per cambiare timeout e intervalli

## File Modificati

### Nuovi File
- `client/src/hooks/use-auth.ts` - Hook personalizzato per gestione autenticazione
- `client/src/config/auth.ts` - Configurazione centralizzata

### File Aggiornati
- `client/src/App.tsx` - Utilizza il nuovo hook useAuth
- `client/src/pages/login.tsx` - Aggiornato per utilizzare la configurazione
- `client/src/pages/tracking.tsx` - Aggiornato per utilizzare il tipo User

## Configurazione

### Modificare il Timeout della Sessione

Nel file `client/src/config/auth.ts`:

```typescript
export const AUTH_CONFIG = {
  // Cambia questo valore per modificare il timeout
  SESSION_TIMEOUT: 8 * 60 * 60 * 1000, // 8 ore
  
  // Intervallo di controllo (ogni quanto verificare se la sessione è scaduta)
  SESSION_CHECK_INTERVAL: 60 * 1000, // 1 minuto
  
  // ... altre configurazioni
};
```

### Esempi di Configurazione

```typescript
// Timeout di 30 minuti
SESSION_TIMEOUT: 30 * 60 * 1000,

// Timeout di 2 ore
SESSION_TIMEOUT: 2 * 60 * 60 * 1000,

// Timeout di 24 ore
SESSION_TIMEOUT: 24 * 60 * 60 * 1000,
```

## Comportamento

### Durante l'Uso
1. L'utente effettua il login
2. Il sistema salva il timestamp di login
3. Ogni minuto verifica se la sessione è scaduta
4. Se scaduta, esegue logout automatico

### Alla Chiusura del Browser
1. L'utente chiude il browser/tab
2. Gli eventi `beforeunload` o `pagehide` vengono attivati
3. I dati di sessione vengono rimossi dal localStorage
4. Al prossimo accesso, l'utente dovrà effettuare nuovamente il login

### Al Riavvio dell'Applicazione
1. L'applicazione carica i dati dal localStorage
2. Se non ci sono dati o sono scaduti, l'utente non è autenticato
3. Se i dati sono validi, l'utente rimane loggato

## Vantaggi

- **Sicurezza**: Sessione automaticamente scaduta dopo un periodo di inattività
- **Pulizia**: Reset automatico alla chiusura del browser
- **Flessibilità**: Configurazione centralizzata e facilmente modificabile
- **Affidabilità**: Controllo periodico durante l'uso dell'applicazione

## Note Tecniche

- Utilizza `localStorage` per la persistenza dei dati
- Eventi del browser per la gestione della chiusura
- Hook React personalizzato per la gestione dello stato
- Configurazione TypeScript per type safety 