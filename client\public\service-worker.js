const CACHE_NAME = 'satellite-tracker-cache-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.json',
  '/assets/logo-ecotrac.png',
  // Aggiungi qui altri file statici se necessario
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll(urlsToCache);
    })
  );
});

self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.filter((name) => name !== CACHE_NAME).map((name) => caches.delete(name))
      );
    })
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request).then((response) => {
      return response || fetch(event.request);
    })
  );
});

// Gestione notifiche push
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event);
  
  let notificationData = {};
  
  if (event.data) {
    try {
      notificationData = event.data.json();
    } catch (e) {
      notificationData = {
        title: 'ECOTrac',
        body: event.data.text() || 'Nuovo aggiornamento disponibile',
      };
    }
  }

  const options = {
    body: notificationData.body || 'Nuovo aggiornamento dai tuoi dispositivi satellitari',
    icon: '/assets/logo-ecotrac.png',
    badge: '/assets/logo-ecotrac.png',
    tag: notificationData.tag || 'satellite-update',
    data: notificationData.data || {},
    actions: [
      {
        action: 'view',
        title: 'Visualizza',
        icon: '/assets/logo-ecotrac.png'
      }
    ],
    requireInteraction: false,
    timestamp: Date.now(),
  };

  event.waitUntil(
    self.registration.showNotification(
      notificationData.title || 'ECOTrac',
      options
    )
  );
});

// Gestione click sulle notifiche
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();

  // Apri l'app o porta il focus su di essa
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Se l'app è già aperta, porta il focus su di essa
      for (const client of clientList) {
        if (client.url.includes('localhost') || client.url.includes(self.location.origin)) {
          return client.focus();
        }
      }
      
      // Altrimenti apri una nuova finestra
      return clients.openWindow('/');
    })
  );
});

// Gestione chiusura notifiche
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event);
}); 