#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync, spawn } from 'child_process';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

console.log('🚀 Starting deployment process...');

// Check if we're on Windows or Linux
const isWindows = process.platform === 'win32';
const isLinux = process.platform === 'linux';

console.log(`📋 Platform detected: ${process.platform}`);

// Clean and build
console.log('🧹 Cleaning and building...');
try {
  execSync('node scripts/clean-build.js', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ Clean build failed:', error.message);
  process.exit(1);
}

// Check if dist/index.js exists
const serverPath = path.join(process.cwd(), 'dist', 'index.js');
if (!fs.existsSync(serverPath)) {
  console.error('❌ Server build not found at:', serverPath);
  process.exit(1);
}

console.log('✅ Build completed, starting production server...');

// Start the production server
const env = { ...process.env, NODE_ENV: 'production' };

if (isWindows) {
  // On Windows, use spawn to keep the process running
  const server = spawn('node', ['dist/index.js'], {
    stdio: 'inherit',
    env: env
  });
  
  server.on('error', (error) => {
    console.error('❌ Server failed to start:', error.message);
    process.exit(1);
  });
  
  server.on('exit', (code) => {
    console.log(`🛑 Server exited with code ${code}`);
    process.exit(code);
  });
} else {
  // On Linux, use execSync to replace the current process
  try {
    execSync('node dist/index.js', { 
      stdio: 'inherit',
      env: env
    });
  } catch (error) {
    console.error('❌ Server failed:', error.message);
    process.exit(1);
  }
}
