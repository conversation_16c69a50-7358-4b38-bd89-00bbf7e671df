import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { fileURLToPath, URL } from 'node:url';

export default defineConfig({
  plugins: [
    react(),
    // Plugin to add cache-busting timestamp to HTML
    {
      name: 'cache-buster',
      transformIndexHtml(html) {
        const timestamp = Date.now();
        return html.replace(
          '<head>',
          `<head>\n    <meta name="build-timestamp" content="${timestamp}" />`
        );
      }
    }
  ],
  resolve: {
    alias: {
      "@": path.resolve(path.dirname(fileURLToPath(import.meta.url)), "client", "src"),
      "@shared": path.resolve(path.dirname(fileURLToPath(import.meta.url)), "shared"),
      "@assets": path.resolve(path.dirname(fileURLToPath(import.meta.url)), "attached_assets"),
    },
  },
  root: path.resolve(path.dirname(fileURLToPath(import.meta.url)), "client"),
  build: {
    outDir: path.resolve(path.dirname(fileURLToPath(import.meta.url)), "dist/public"),
    emptyOutDir: true,
    rollupOptions: {
      output: {
        // Force new file names on each build to prevent caching issues
        entryFileNames: () => {
          const timestamp = Date.now();
          return `assets/[name]-${timestamp}-[hash].js`;
        },
        chunkFileNames: () => {
          const timestamp = Date.now();
          return `assets/[name]-${timestamp}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          const timestamp = Date.now();
          const ext = assetInfo.name?.split('.').pop();
          return `assets/[name]-${timestamp}-[hash].${ext}`;
        }
      }
    }
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"],
    },
    middlewareMode: false,
    headers: {
      'Cache-Control': 'no-cache'
    },
    allowedHosts: ['localhost', '127.0.0.1'],
    host: '0.0.0.0',
    port: 5173,
    strictPort: false,
    cors: {
      origin: ['http://localhost:5000'],
      credentials: true
    }
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
  optimizeDeps: {
    include: ['react', 'react-dom']
  },
});
